// اختبار سريع للتحقق من إصلاحات الصور
console.log('🧪 بدء اختبار إصلاحات الصور...');

// محاكاة بيانات ثغرة للاختبار
const testVulnerability = {
    name: 'API Authentication Bypass',
    type: 'API Authentication Bypass',
    url: 'http://testphp.vulnweb.com',
    target_url: 'http://testphp.vulnweb.com',
    screenshots: {
        folder: 'testphp_vulnweb_com',
        before: './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/before_API_Authentication_Bypass.png',
        during: './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/during_API_Authentication_Bypass.png',
        after: './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/after_API_Authentication_Bypass.png'
    },
    visual_proof: {
        before_screenshot_path: './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/before_API_Authentication_Bypass.png',
        during_screenshot_path: './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/during_API_Authentication_Bypass.png',
        after_screenshot_path: './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/after_API_Authentication_Bypass.png'
    }
};

// محاكاة BugBountyCore للاختبار
const mockBugBountyCore = {
    analysisState: {
        currentScanFolder: 'testphp_vulnweb_com'
    },
    
    getCleanVulnerabilityName(vuln) {
        return (vuln.name || vuln.type || 'unknown').replace(/[^a-zA-Z0-9_]/g, '_');
    },
    
    getCorrectFolderName(vuln) {
        if (vuln.screenshots && vuln.screenshots.folder) {
            return vuln.screenshots.folder;
        }
        if (this.analysisState && this.analysisState.currentScanFolder) {
            return this.analysisState.currentScanFolder;
        }
        const url = vuln.url || vuln.target_url || 'default';
        return url.replace(/https?:\/\//, '').replace(/[\/\?&=\.]/g, '_').replace(/[<>:"|*]/g, '');
    },
    
    getCorrectImageName(stage, vuln) {
        const cleanVulnName = this.getCleanVulnerabilityName(vuln);
        return `${stage}_${cleanVulnName}.png`;
    },
    
    // الدالة المُصلحة
    findRealImageForVulnerability(vuln, stage) {
        console.log(`🔍 البحث عن مسار صورة ${stage} للثغرة: ${vuln.name}`);

        const cleanVulnName = this.getCleanVulnerabilityName(vuln);
        let folderName = this.getCorrectFolderName(vuln);

        if (vuln.screenshots && vuln.screenshots.folder) {
            folderName = vuln.screenshots.folder;
            console.log(`🔍 استخدام مجلد الصور من بيانات الثغرة: ${folderName}`);
        } else if (this.analysisState?.currentScanFolder) {
            folderName = this.analysisState.currentScanFolder;
            console.log(`🔍 استخدام مجلد الفحص الحالي: ${folderName}`);
        }

        const imageName = this.getCorrectImageName(stage, vuln);
        const imagePath = `./assets/modules/bugbounty/screenshots/${folderName}/${imageName}`;

        console.log(`🔍 مسار الصورة المُنشأ: ${imagePath}`);

        // البحث في مسارات الصور المحفوظة أولاً
        if (vuln.screenshots && vuln.screenshots[stage]) {
            const savedPath = vuln.screenshots[stage];
            if (typeof savedPath === 'string' && savedPath.includes('./assets/modules/bugbounty/screenshots/')) {
                console.log(`✅ تم العثور على مسار صورة ${stage} محفوظ: ${savedPath}`);
                return savedPath;
            }
        }

        // البحث في visual_proof paths
        if (vuln.visual_proof && vuln.visual_proof[`${stage}_screenshot_path`]) {
            const savedPath = vuln.visual_proof[`${stage}_screenshot_path`];
            console.log(`✅ تم العثور على مسار صورة ${stage} في visual_proof: ${savedPath}`);
            return savedPath;
        }

        console.log(`📁 استخدام المسار المُنشأ: ${imagePath}`);
        return imagePath;
    }
};

// اختبار الدوال
console.log('\n🧪 اختبار getCorrectFolderName:');
const folderName = mockBugBountyCore.getCorrectFolderName(testVulnerability);
console.log(`📂 اسم المجلد: ${folderName}`);

console.log('\n🧪 اختبار getCleanVulnerabilityName:');
const cleanName = mockBugBountyCore.getCleanVulnerabilityName(testVulnerability);
console.log(`🏷️ اسم الثغرة المُنظف: ${cleanName}`);

console.log('\n🧪 اختبار getCorrectImageName:');
const beforeImageName = mockBugBountyCore.getCorrectImageName('before', testVulnerability);
const duringImageName = mockBugBountyCore.getCorrectImageName('during', testVulnerability);
const afterImageName = mockBugBountyCore.getCorrectImageName('after', testVulnerability);
console.log(`📸 اسم صورة قبل: ${beforeImageName}`);
console.log(`📸 اسم صورة أثناء: ${duringImageName}`);
console.log(`📸 اسم صورة بعد: ${afterImageName}`);

console.log('\n🧪 اختبار findRealImageForVulnerability:');
const beforePath = mockBugBountyCore.findRealImageForVulnerability(testVulnerability, 'before');
const duringPath = mockBugBountyCore.findRealImageForVulnerability(testVulnerability, 'during');
const afterPath = mockBugBountyCore.findRealImageForVulnerability(testVulnerability, 'after');

console.log(`📍 مسار صورة قبل: ${beforePath}`);
console.log(`📍 مسار صورة أثناء: ${duringPath}`);
console.log(`📍 مسار صورة بعد: ${afterPath}`);

// التحقق من صحة المسارات
console.log('\n✅ التحقق من صحة المسارات:');
const expectedPaths = [
    './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/before_API_Authentication_Bypass.png',
    './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/during_API_Authentication_Bypass.png',
    './assets/modules/bugbounty/screenshots/testphp_vulnweb_com/after_API_Authentication_Bypass.png'
];

const actualPaths = [beforePath, duringPath, afterPath];
const stages = ['before', 'during', 'after'];

let allCorrect = true;
for (let i = 0; i < 3; i++) {
    if (actualPaths[i] === expectedPaths[i]) {
        console.log(`✅ مسار ${stages[i]} صحيح`);
    } else {
        console.log(`❌ مسار ${stages[i]} خاطئ:`);
        console.log(`   المتوقع: ${expectedPaths[i]}`);
        console.log(`   الفعلي: ${actualPaths[i]}`);
        allCorrect = false;
    }
}

console.log(`\n🎯 النتيجة النهائية: ${allCorrect ? '✅ جميع الإصلاحات تعمل بشكل صحيح!' : '❌ هناك مشاكل تحتاج إصلاح'}`);
