// اختبار الإصلاحات النهائية للصور في النظام الحقيقي

const fs = require('fs');

console.log('🔍 اختبار الإصلاحات النهائية للصور...');
console.log('=========================================');

// فحص الإصلاحات المطبقة
function checkImagePathFixes() {
    console.log('\n📁 فحص إصلاحات مسارات الصور:');
    
    try {
        const coreContent = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');
        
        const fixes = [
            {
                name: 'إصلاح getCorrectImagePath - إزالة ./',
                pattern: 'assets/modules/bugbounty/screenshots/${folderName}/${imageName}',
                found: coreContent.includes('assets/modules/bugbounty/screenshots/${folderName}/${imageName}') && 
                       !coreContent.includes('./assets/modules/bugbounty/screenshots/${folderName}/${imageName}')
            },
            {
                name: 'إصلاح base64 مباشر في generateImpactVisualizations',
                pattern: 'data:image/png;base64,${beforeImage}',
                found: coreContent.includes('data:image/png;base64,${beforeImage}')
            },
            {
                name: 'إصلاح base64 مباشر للصور during',
                pattern: 'data:image/png;base64,${duringImage}',
                found: coreContent.includes('data:image/png;base64,${duringImage}')
            },
            {
                name: 'إصلاح base64 مباشر للصور after',
                pattern: 'data:image/png;base64,${afterImage}',
                found: coreContent.includes('data:image/png;base64,${afterImage}')
            },
            {
                name: 'إصلاح base64 مباشر في تقارير markdown',
                pattern: 'data:image/png;base64,${screenshots.before}',
                found: coreContent.includes('data:image/png;base64,${screenshots.before}')
            }
        ];
        
        fixes.forEach(fix => {
            const status = fix.found ? '✅' : '❌';
            console.log(`   ${status} ${fix.name}`);
        });
        
        const appliedFixes = fixes.filter(f => f.found).length;
        console.log(`\n📊 تم تطبيق ${appliedFixes}/${fixes.length} إصلاحات`);
        
        return appliedFixes === fixes.length;
        
    } catch (error) {
        console.error('❌ خطأ في فحص الإصلاحات:', error);
        return false;
    }
}

// فحص مشاكل المسارات القديمة
function checkOldPathIssues() {
    console.log('\n🔍 فحص المسارات القديمة المُشكلة:');
    
    try {
        const coreContent = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');
        
        const oldPatterns = [
            {
                name: 'مسارات تبدأ بـ ./ (مُشكلة)',
                pattern: './assets/modules/bugbounty/screenshots',
                found: coreContent.includes('./assets/modules/bugbounty/screenshots')
            },
            {
                name: 'استخدام getCorrectImagePath مع مسارات خاطئة',
                pattern: 'getCorrectImagePath(vulnerability, \'before\')',
                found: coreContent.includes('getCorrectImagePath(vulnerability, \'before\')')
            }
        ];
        
        oldPatterns.forEach(pattern => {
            const status = pattern.found ? '⚠️' : '✅';
            const message = pattern.found ? '(لا تزال موجودة - تحتاج إصلاح)' : '(تم إصلاحها)';
            console.log(`   ${status} ${pattern.name} ${message}`);
        });
        
        const remainingIssues = oldPatterns.filter(p => p.found).length;
        console.log(`\n📊 المشاكل المتبقية: ${remainingIssues}/${oldPatterns.length}`);
        
        return remainingIssues === 0;
        
    } catch (error) {
        console.error('❌ خطأ في فحص المسارات القديمة:', error);
        return false;
    }
}

// محاكاة اختبار دالة getCorrectImagePath
function testGetCorrectImagePath() {
    console.log('\n🧪 اختبار دالة getCorrectImagePath:');
    
    // محاكاة الدالة المُصلحة
    function mockGetCorrectImagePath(vulnerability, stage) {
        const folderName = 'testphp_vulnweb_com'; // محاكاة
        const imageName = `${stage}_${vulnerability.name.replace(/\s+/g, '_')}.png`;
        // الإصلاح: إزالة ./ من بداية المسار
        return `assets/modules/bugbounty/screenshots/${folderName}/${imageName}`;
    }
    
    const testVulnerability = { name: 'API Authentication Bypass' };
    
    const beforePath = mockGetCorrectImagePath(testVulnerability, 'before');
    const duringPath = mockGetCorrectImagePath(testVulnerability, 'during');
    const afterPath = mockGetCorrectImagePath(testVulnerability, 'after');
    
    console.log(`   📸 Before: ${beforePath}`);
    console.log(`   📸 During: ${duringPath}`);
    console.log(`   📸 After: ${afterPath}`);
    
    // فحص إذا كانت المسارات صحيحة (لا تبدأ بـ ./)
    const pathsCorrect = !beforePath.startsWith('./') && 
                        !duringPath.startsWith('./') && 
                        !afterPath.startsWith('./');
    
    const status = pathsCorrect ? '✅' : '❌';
    console.log(`   ${status} المسارات ${pathsCorrect ? 'صحيحة' : 'خاطئة'}`);
    
    return pathsCorrect;
}

// محاكاة اختبار base64
function testBase64Handling() {
    console.log('\n🧪 اختبار معالجة Base64:');
    
    const mockBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    
    // محاكاة الكود المُصلح
    function mockGenerateImageHtml(base64Data, stage) {
        if (base64Data && base64Data.includes('base64') && base64Data.length > 100) {
            return `<img src="data:image/png;base64,${base64Data}" alt="${stage}" style="width: 100%; max-height: 150px; object-fit: cover; border-radius: 5px;" />`;
        }
        return `<div>لا توجد صورة ${stage}</div>`;
    }
    
    const beforeHtml = mockGenerateImageHtml(mockBase64, 'قبل الاستغلال');
    const duringHtml = mockGenerateImageHtml(mockBase64, 'أثناء الاستغلال');
    const afterHtml = mockGenerateImageHtml(mockBase64, 'بعد الاستغلال');
    
    console.log(`   📸 Before HTML: ${beforeHtml.substring(0, 50)}...`);
    console.log(`   📸 During HTML: ${duringHtml.substring(0, 50)}...`);
    console.log(`   📸 After HTML: ${afterHtml.substring(0, 50)}...`);
    
    // فحص إذا كان HTML يحتوي على data:image
    const htmlCorrect = beforeHtml.includes('data:image/png;base64,') &&
                       duringHtml.includes('data:image/png;base64,') &&
                       afterHtml.includes('data:image/png;base64,');
    
    const status = htmlCorrect ? '✅' : '❌';
    console.log(`   ${status} HTML ${htmlCorrect ? 'صحيح' : 'خاطئ'}`);
    
    return htmlCorrect;
}

// تشغيل جميع الاختبارات
async function runAllTests() {
    console.log('🧪 بدء جميع اختبارات الإصلاحات...');
    
    const pathFixesApplied = checkImagePathFixes();
    const oldIssuesFixed = checkOldPathIssues();
    const pathTestPassed = testGetCorrectImagePath();
    const base64TestPassed = testBase64Handling();
    
    console.log('\n📋 النتيجة النهائية:');
    console.log('==================');
    
    const allTestsPassed = pathFixesApplied && oldIssuesFixed && pathTestPassed && base64TestPassed;
    
    if (allTestsPassed) {
        console.log('🎉 جميع الإصلاحات مطبقة بنجاح!');
        console.log('✅ مسارات الصور مُصلحة');
        console.log('✅ معالجة Base64 محسنة');
        console.log('✅ المسارات القديمة المُشكلة تم إصلاحها');
        console.log('🚀 النظام جاهز لإنتاج تقارير بصور صحيحة');
    } else {
        console.log('⚠️ بعض الإصلاحات تحتاج مراجعة:');
        console.log(`   📁 إصلاحات المسارات: ${pathFixesApplied ? '✅' : '❌'}`);
        console.log(`   🔍 المسارات القديمة: ${oldIssuesFixed ? '✅' : '❌'}`);
        console.log(`   🧪 اختبار المسارات: ${pathTestPassed ? '✅' : '❌'}`);
        console.log(`   🧪 اختبار Base64: ${base64TestPassed ? '✅' : '❌'}`);
    }
    
    console.log('\n🎯 الخطوات التالية:');
    if (allTestsPassed) {
        console.log('1. تشغيل Bug Bounty v4.0 من الواجهة الرئيسية');
        console.log('2. فحص موقع testphp.vulnweb.com');
        console.log('3. التحقق من عرض الصور بشكل صحيح في التقرير');
        console.log('4. التأكد من عدم ظهور رسائل "فشل في تحميل الصورة"');
    } else {
        console.log('1. مراجعة الإصلاحات المفقودة أعلاه');
        console.log('2. تطبيق الإصلاحات المتبقية');
        console.log('3. إعادة تشغيل الاختبار');
    }
    
    return allTestsPassed;
}

// تشغيل الاختبارات
runAllTests().then(success => {
    console.log(`\n🚀 اكتمل الاختبار! النتيجة: ${success ? 'نجح' : 'فشل'}`);
}).catch(error => {
    console.error('❌ خطأ في الاختبار:', error);
});
