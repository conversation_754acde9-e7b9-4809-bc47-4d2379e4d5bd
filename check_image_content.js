// فحص محتوى الصور للتحقق من نوعها
const fs = require('fs');
const path = require('path');

console.log('🔍 فحص محتوى الصور...');

const screenshotsDir = 'assets/modules/bugbounty/screenshots/testphp_vulnweb_com';

// فحص صورة before (صغيرة)
const beforeFile = path.join(screenshotsDir, 'before_SQL_Injection.png');
const afterFile = path.join(screenshotsDir, 'after_SQL_Injection.png');

if (fs.existsSync(beforeFile)) {
    const beforeStats = fs.statSync(beforeFile);
    console.log(`📸 صورة before: ${beforeStats.size} bytes`);
    
    // قراءة أول 100 بايت لفحص النوع
    const beforeBuffer = fs.readFileSync(beforeFile);
    const beforeHeader = beforeBuffer.slice(0, 20).toString('hex');
    console.log(`🔍 header صورة before: ${beforeHeader}`);
    
    // التحقق من أنها PNG حقيقية
    if (beforeHeader.startsWith('89504e47')) {
        console.log('✅ صورة before هي PNG حقيقية');
    } else {
        console.log('❌ صورة before ليست PNG صحيحة');
    }
}

if (fs.existsSync(afterFile)) {
    const afterStats = fs.statSync(afterFile);
    console.log(`📸 صورة after: ${afterStats.size} bytes`);
    
    // قراءة أول 100 بايت لفحص النوع
    const afterBuffer = fs.readFileSync(afterFile);
    const afterHeader = afterBuffer.slice(0, 20).toString('hex');
    console.log(`🔍 header صورة after: ${afterHeader}`);
    
    // التحقق من أنها PNG حقيقية
    if (afterHeader.startsWith('89504e47')) {
        console.log('✅ صورة after هي PNG حقيقية');
    } else {
        console.log('❌ صورة after ليست PNG صحيحة');
    }
    
    // فحص إضافي للصور الكبيرة - البحث عن نص SVG
    const afterContent = afterBuffer.toString('utf8', 0, Math.min(1000, afterBuffer.length));
    if (afterContent.includes('<svg') || afterContent.includes('<?xml')) {
        console.log('⚠️ صورة after تحتوي على محتوى SVG/XML - هذه صورة مُنشأة وليست لقطة شاشة حقيقية');
    } else {
        console.log('✅ صورة after تبدو كصورة حقيقية');
    }
}

// فحص جميع الصور الموجودة
console.log('\n📊 إحصائيات جميع الصور:');
const files = fs.readdirSync(screenshotsDir);
const imageFiles = files.filter(f => f.endsWith('.png'));

let beforeCount = 0, duringCount = 0, afterCount = 0;
let beforeSizes = [], duringSizes = [], afterSizes = [];

imageFiles.forEach(file => {
    const filePath = path.join(screenshotsDir, file);
    const stats = fs.statSync(filePath);
    
    if (file.startsWith('before_')) {
        beforeCount++;
        beforeSizes.push(stats.size);
    } else if (file.startsWith('during_')) {
        duringCount++;
        duringSizes.push(stats.size);
    } else if (file.startsWith('after_')) {
        afterCount++;
        afterSizes.push(stats.size);
    }
});

console.log(`📸 صور before: ${beforeCount} صورة`);
if (beforeSizes.length > 0) {
    const avgBefore = Math.round(beforeSizes.reduce((a, b) => a + b, 0) / beforeSizes.length);
    console.log(`   متوسط الحجم: ${avgBefore} bytes`);
    console.log(`   النطاق: ${Math.min(...beforeSizes)} - ${Math.max(...beforeSizes)} bytes`);
}

console.log(`📸 صور during: ${duringCount} صورة`);
if (duringSizes.length > 0) {
    const avgDuring = Math.round(duringSizes.reduce((a, b) => a + b, 0) / duringSizes.length);
    console.log(`   متوسط الحجم: ${avgDuring} bytes`);
    console.log(`   النطاق: ${Math.min(...duringSizes)} - ${Math.max(...duringSizes)} bytes`);
}

console.log(`📸 صور after: ${afterCount} صورة`);
if (afterSizes.length > 0) {
    const avgAfter = Math.round(afterSizes.reduce((a, b) => a + b, 0) / afterSizes.length);
    console.log(`   متوسط الحجم: ${avgAfter} bytes`);
    console.log(`   النطاق: ${Math.min(...afterSizes)} - ${Math.max(...afterSizes)} bytes`);
}

// التحليل
console.log('\n🎯 التحليل:');
if (afterSizes.length > 0 && beforeSizes.length > 0) {
    const avgAfter = afterSizes.reduce((a, b) => a + b, 0) / afterSizes.length;
    const avgBefore = beforeSizes.reduce((a, b) => a + b, 0) / beforeSizes.length;
    
    if (avgAfter > avgBefore * 10) {
        console.log('⚠️ صور after أكبر بكثير من صور before - قد تكون صور مُنشأة بدلاً من لقطات شاشة حقيقية');
        console.log('💡 الحل: تعديل دالة captureAfterExploitationScreenshot لتستخدم نفس طريقة before و during');
    } else {
        console.log('✅ أحجام الصور متقاربة - تبدو طبيعية');
    }
}

console.log('\n✅ انتهى فحص الصور');
