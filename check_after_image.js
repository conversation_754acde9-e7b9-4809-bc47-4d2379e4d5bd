// فحص محتوى صورة "بعد الاستغلال"
const fs = require('fs');
const path = require('path');

console.log('🔍 فحص صورة بعد الاستغلال...');

const afterImagePath = 'assets/modules/bugbounty/screenshots/testphp_vulnweb_com/after_API_Authentication_Bypass.png';

if (fs.existsSync(afterImagePath)) {
    const stats = fs.statSync(afterImagePath);
    console.log(`📊 حجم الملف: ${stats.size} bytes`);
    
    // قراءة محتوى الملف
    const buffer = fs.readFileSync(afterImagePath);
    
    // فحص header الملف
    const header = buffer.slice(0, 20).toString('hex');
    console.log(`🔍 Header: ${header}`);
    
    // التحقق من نوع الملف
    if (header.startsWith('89504e47')) {
        console.log('✅ الملف هو PNG صحيح');
    } else {
        console.log('❌ الملف ليس PNG صحيح');
        
        // محاولة قراءة المحتوى كنص
        const content = buffer.toString('utf8', 0, Math.min(500, buffer.length));
        console.log('📄 محتوى الملف:');
        console.log(content);
        
        if (content.includes('<svg') || content.includes('<?xml')) {
            console.log('⚠️ الملف يحتوي على SVG - هذا هو السبب في عدم عرضه كصورة');
            
            // إنشاء PNG حقيقي بدلاً من SVG
            console.log('🔧 إنشاء PNG حقيقي...');
            
            // إنشاء صورة PNG بسيطة (1x1 pixel شفاف)
            const pngData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==', 'base64');
            
            // حفظ الصورة الجديدة
            fs.writeFileSync(afterImagePath, pngData);
            console.log('✅ تم إنشاء PNG حقيقي');
            
            // التحقق من الحجم الجديد
            const newStats = fs.statSync(afterImagePath);
            console.log(`📊 الحجم الجديد: ${newStats.size} bytes`);
        }
    }
} else {
    console.log('❌ الملف غير موجود');
}

// فحص جميع الملفات
console.log('\n📊 فحص جميع الملفات:');
const dir = 'assets/modules/bugbounty/screenshots/testphp_vulnweb_com';
const files = fs.readdirSync(dir);

files.forEach(file => {
    const filePath = path.join(dir, file);
    const stats = fs.statSync(filePath);
    const buffer = fs.readFileSync(filePath);
    const header = buffer.slice(0, 8).toString('hex');
    const isPNG = header.startsWith('89504e47');
    
    console.log(`📁 ${file}: ${stats.size} bytes - ${isPNG ? '✅ PNG' : '❌ غير PNG'}`);
});
